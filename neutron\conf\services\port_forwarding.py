#
#    Licensed under the Apache License, Version 2.0 (the "License"); you may
#    not use this file except in compliance with the License. You may obtain
#    a copy of the License at
#
#         http://www.apache.org/licenses/LICENSE-2.0
#
#    Unless required by applicable law or agreed to in writing, software
#    distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
#    WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
#    License for the specific language governing permissions and limitations
#    under the License.
#

from oslo_config import cfg

from neutron._i18n import _

PORT_FORWARDING_OPTS = [
    cfg.IntOpt('port_forwarding_limit_per_port', default=0,
               help=_("Maximum port forwarding per port")),
    cfg.IntOpt('port_forwarding_limit_per_floatingip', default=0,
               help=_("Maximum port forwarding per floatingip")),
    cfg.IntOpt('port_forwarding_limit_per_router', default=0,
               help=_("Maximum port forwarding per router")),
    cfg.BoolOpt('port_forwarding_check_fast', default=False,
                help=_("Whether check port forwarding resources by using"
                       "fast SQL method.")),
    cfg.BoolOpt('allow_port_forwarding_internal_port_range_overlapping',
                default=False,
                help=_('Whether allow port forwarding internal port range'
                       'overlapping under same internal_neutron_port_id,'
                       'protocol and internal_ip_address.')),
]


def register_elastic_snat_db_opts(conf=cfg.CONF):
    conf.register_opts(PORT_FORWARDING_OPTS)

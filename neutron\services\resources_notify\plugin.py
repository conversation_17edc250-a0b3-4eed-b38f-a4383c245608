#
#    Licensed under the Apache License, Version 2.0 (the "License"); you may
#    not use this file except in compliance with the License. You may obtain
#    a copy of the License at
#
#         http://www.apache.org/licenses/LICENSE-2.0
#
#    Unless required by applicable law or agreed to in writing, software
#    distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
#    WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
#    License for the specific language governing permissions and limitations
#    under the License.

from neutron_lib.callbacks import events
from neutron_lib.callbacks import exceptions
from neutron_lib.callbacks import registry
from neutron_lib.callbacks import resources
from neutron_lib import exceptions as n_exc
from neutron_lib.exceptions import l3 as l3_exc
from neutron_lib.plugins import constants
from neutron_lib.plugins import directory
from neutron_lib.plugins import constants as plugin_constants
from oslo_config import cfg
from oslo_log import log as logging

from neutron.api.rpc.callbacks import events as rpc_events
from neutron.api.rpc.handlers import resources_rpc
from neutron.conf.db import l3_db
from neutron.conf.db import l3_dvr_db
from neutron.conf.services import port_forwarding as port_forwarding_conf
from neutron.common import exceptions as common_exc
from neutron.extensions import _resources_notify as api_def
from neutron.extensions import resources_notify as ext_resources_notify
from neutron.extensions import _elastic_snat as esnat_def
from neutron.objects import elastic_snat as elastic_snat_obj
from neutron.objects import port_forwarding as port_forwarding_obj
from neutron.services.portforwarding.common import exceptions as pf_exc

LOG = logging.getLogger(__name__)

l3_db.register_db_l3_opts()
l3_dvr_db.register_db_l3_dvr_opts()
port_forwarding_conf.register_elastic_snat_db_opts()
NO_VALID_HOST = "NO-VALID-HOST"


class ResourcesNotifyPlugin(ext_resources_notify.ResourcesNotifyPluginBase):
    supported_extension_aliases = [api_def.ALIAS]

    def __init__(self):
        super(ResourcesNotifyPlugin, self).__init__()
        self.handler_map = self._build_handler_map()
        self.push_api = resources_rpc.ResourcesPushRpcApi()
        self.core_plugin = directory.get_plugin()
        self.l3_plugin = directory.get_plugin(constants.L3)
        self.pf_plugin = directory.get_plugin(plugin_constants.PORTFORWARDING)
        self.elastic_snat_plugin = directory.get_plugin(esnat_def.ELASTIC_SNAT)

    def _build_handler_map(self):
        handler_map = {}
        for resource in api_def.RESOURCES:
            for event in api_def.EVENTS:
                method_name = "{}_{}".format(resource, event)
                handler_map[(resource, event)] = (
                    getattr(self, method_name, self.default_handler))
        return handler_map

    def resources_notify(self, context, body, **kwargs):
        resources_notify = body.get(api_def.RESOURCE_NAME)
        resource_type = resources_notify.get("resource_type")
        event_type = resources_notify.get("event_type")
        data = resources_notify.get("resource", {})

        handler = self.handler_map.get((resource_type, event_type),
                                       self.default_handler)

        return handler(context, data)

    def network_created(self, data):
        return {"message": "network created"}

    def network_updated(self, data):
        return {"message": "network updated"}

    def network_deleted(self, data):
        return {"message": "network deleted"}

    def subnet_created(self, data):
        return {"message": "subnet created"}

    def subnet_updated(self, data):
        return {"message": "subnet updated"}

    def subnet_deleted(self, data):
        return {"message": "subnet deleted"}

    def port_created(self, data):
        return {"message": "port created"}

    def port_updated(self, data):
        return {"message": "port updated"}

    def port_deleted(self, data):
        return {"message": "port deleted"}

    def flow_log_created(self, data):
        return {"message": "flow_log created"}

    def flow_log_updated(self, data):
        return {"message": "flow_log updated"}

    def flow_log_deleted(self, data):
        return {"message": "flow_log deleted"}

    def qos_policy_created(self, data):
        return {"message": "qos_policy created"}

    def qos_policy_updated(self, data):
        return {"message": "qos_policy updated"}

    def qos_policy_deleted(self, data):
        return {"message": "qos_policy deleted"}

    def qos_rule_created(self, data):
        return {"message": "qos_rule created"}

    def qos_rule_updated(self, data):
        return {"message": "qos_rule updated"}

    def qos_rule_deleted(self, data):
        return {"message": "qos_rule deleted"}

    def _raise_router_exception(self, msg):
        LOG.error(msg)
        raise n_exc.BadRequest(resource='router', msg=msg)

    def router_created(self, context, data):
        router = data.get("router")
        router_db = self.l3_plugin._get_router(context, router['id'])
        new_router = self.l3_plugin._make_router_dict(router_db)
        # registry.notify(resources.ROUTER, events.BEFORE_CREATE,
        #                 self, context=context, router=router)
        # registry.notify(resources.ROUTER, events.PRECOMMIT_CREATE,
        #                 self, context=context, router=router,
        #                 router_id=router['id'], router_db=router_db)
        # registry.notify(resources.ROUTER, events.AFTER_CREATE, self,
        #                 context=context, router_id=router_db.id,
        #                 router=new_router, request_attrs=router,
        #                 router_db=router_db)
        if (cfg.CONF.router_limits_scheduler_enable or
                new_router.get('external_gateway_info')):
            try:
                self.l3_plugin.notify_router_updated(context, new_router['id'], None)
            except common_exc.FailedToScheduleRouter:
                msg = 'No valid host found for router: %s' % new_router['id']
                self._raise_router_exception(msg)
        if (cfg.CONF.router_limits_scheduler_enable and
                new_router['status'] == NO_VALID_HOST):
            msg = 'No valid host found for router: %s' % new_router['id']
            self._raise_router_exception(msg)
        return {"message": "router created"}

    def router_updated(self, context, data):
        router = data.get("router")
        old_router = data.get("old_router")
        router_db = self.l3_plugin._get_router(context, router['id'])
        # need to handle the port notification logic for gatewayinfo?
        registry.publish(resources.ROUTER, events.PRECOMMIT_UPDATE,
                         self, payload=events.DBEventPayload(
                            context, request_body=router,
                            states=(old_router,), resource_id=router['id'],
                            desired_state=router_db))
        if 'ha' in router or 'distributed' in router:
            self.l3_plugin.after_migration(context, old_router, router_db)
        updated = self.l3_plugin._make_router_dict(router_db)
        if self.l3_plugin._is_router_total_limits_supported:
            if ('total_bandwidth' in router) or (
                    'total_packet_rate' in router) or (
                    'total_connection' in router):
                total_bandwidth, total_packet_rate, total_connection = \
                    self.l3_plugin._get_router_total_limits(
                        context, router)
                updated['total_bandwidth'] = total_bandwidth
                updated['total_packet_rate'] = total_packet_rate
                updated['total_connection'] = total_connection
        registry.notify(resources.ROUTER, events.AFTER_UPDATE, self,
                        context=context, router_id=router['id'], old_router=old_router,
                        router=updated, request_attrs=router, router_db=router_db)
        try:
            self.l3_plugin.notify_router_updated(context, router['id'], None)
        except common_exc.FailedToScheduleRouter:
            msg = 'No valid host found for router: %s' % router['id']
            self._raise_router_exception(msg)
        return {"message": "router updated"}

    def router_deleted(self, context, data):
        router = data.get("router")
        router_db = self.l3_plugin._ensure_router_not_in_use(context, router['id'])
        original = self.l3_plugin._make_router_dict(router_db)
        # registry.publish(resources.ROUTER, events.BEFORE_DELETE, self,
        #                  payload=events.DBEventPayload(
        #                      context, resource_id=router['id']))
        try:
            registry.publish(resources.ROUTER_GATEWAY,
                             events.BEFORE_DELETE, self,
                             payload=events.DBEventPayload(
                                 context, states=(router_db,),
                                 resource_id=router['id']))
        except exceptions.CallbackFailure as e:
                # NOTE(armax): preserve old check's behavior
                if len(e.errors) == 1:
                    raise e.errors[0].error
                raise l3_exc.RouterInUse(router_id=router['id'], reason=e)

        gw_ips = [x['ip_address'] for x in router_db.gw_port['fixed_ips']]

        registry.notify(resources.ROUTER_GATEWAY,
                        events.AFTER_DELETE, self,
                        router_id=router['id'],
                        context=context,
                        router=router_db,
                        network_id=router_db.gw_port['network_id'],
                        new_network_id=None,
                        gateway_ips=gw_ips)
        registry.notify(resources.ROUTER, events.PRECOMMIT_DELETE,
                        self, context=context, router_db=router_db,
                        router_id=router['id'], original=original)

        registry.notify(resources.ROUTER, events.AFTER_DELETE, self,
                        context=context, router_id=router['id'], original=original)
        self.l3_plugin.notify_router_deleted(context, router['id'])
        return {"message": "router deleted"}

    def router_interface_created(self, context, data):
        router_interface = data.get("router_interface")
        self.l3_plugin.notify_router_interface_action(
            context, router_interface, 'add')
        return {"message": "router_interface created"}

    def router_interface_updated(self, context, data):
        router_interface = data.get("router_interface")
        router_id = data.get("router_id")
        registry.notify(resources.ROUTER_INTERFACE,
                        events.AFTER_UPDATE,
                        self,
                        context=context,
                        router_id=router_id,
                        interface_info=router_interface)
        return {"message": "router_interface updated"}

    def router_interface_deleted(self, context, data):
        router_interface = data.get("router_interface")
        self.l3_plugin.notify_router_interface_action(
            context, router_interface, 'remove')
        return {"message": "router_interface deleted"}

    def floating_ip_created(self, context, data):
        floatingip = data.get("floatingip")
        try:
            # request_body need {'floatingip': {'id': 'xxx', 'xxx': 'xxx'}}
            registry.publish(resources.FLOATING_IP, events.BEFORE_CREATE,
                             self, payload=events.DBEventPayload(
                    context, request_body={'floatingip': floatingip}))
        except exceptions.CallbackFailure as e:
            # raise the underlying exception
            raise e.errors[0].error
        fip_id = floatingip['id']
        floatingip_obj = self.l3_plugin._get_floatingip(context,
                                                        fip_id)
        registry.notify(resources.FLOATING_IP, events.PRECOMMIT_CREATE,
                        self, context=context, floatingip=floatingip,
                        floatingip_id=fip_id,
                        floatingip_db=floatingip_obj.db_obj)
        external_port = self.core_plugin.get_port(
            context.elevated(), floatingip_obj.floating_port_id)
        assoc_result = self.l3_plugin._update_fip_assoc(
            context, floatingip, floatingip_obj, external_port)
        registry.notify(resources.FLOATING_IP,
                        events.AFTER_UPDATE,
                        self.l3_plugin._update_fip_assoc,
                        **assoc_result)
        self.l3_plugin._notify_floating_ip_change(context, floatingip)
        return {"message": "floating_ip created"}

    def floating_ip_updated(self, context, data):
        # Ensure that both project_id and tenant_id
        # attributes are present.
        floatingip = data.get("floatingip")
        try:
            # request_body need {'floatingip': {'id': 'xxx', 'xxx': 'xxx'}}
            registry.publish(resources.FLOATING_IP, events.BEFORE_UPDATE,
                             self, payload=events.DBEventPayload(
                    context,
                    request_body={'floatingip': floatingip},
                    resource_id=floatingip['id']))
        except exceptions.CallbackFailure as e:
            # raise the underlying exception
            raise e.errors[0].error

        # Need old_floatingip object transfer to dict.
        # Ensure that both project_id and tenant_id
        # attributes are present.
        old_floatingip = data.get("old_floatingip")
        fip_id = floatingip['id']
        floatingip_obj = self.l3_plugin._get_floatingip(context,
                                                        fip_id)
        external_port = self.core_plugin.get_port(
            context.elevated(), floatingip_obj.floating_port_id)
        assoc_result = self.l3_plugin._update_fip_assoc(
            context, floatingip, floatingip_obj, external_port)
        registry.notify(resources.FLOATING_IP,
                        events.PRECOMMIT_UPDATE,
                        self,
                        floatingip={'floatingip': floatingip},
                        floatingip_db=floatingip_obj.db_obj,
                        old_floatingip=old_floatingip,
                        **assoc_result)

        registry.notify(resources.FLOATING_IP,
                        events.AFTER_UPDATE,
                        self.l3_plugin._update_fip_assoc,
                        **assoc_result)
        self.l3_plugin._notify_floating_ip_change(context, old_floatingip)
        if (floatingip['router_id'] != old_floatingip['router_id'] or
                floatingip['port_id'] != old_floatingip['port_id']):
            self.l3_plugin._notify_floating_ip_change(context, floatingip)
        return {"message": "floating_ip updated"}

    def floating_ip_deleted(self, context, data):
        # Please call this API first before deleting the object.
        floatingip = data.get("floatingip")
        # fip_id = floatingip['id']
        # floatingip_obj = self.l3_plugin._get_floatingip(context,
        #                                                 fip_id)
        # floatingip_dict = self.l3_plugin._make_floatingip_dict(floatingip_obj)
        try:
            # request_body need {'floatingip': {'id': 'xxx', 'xxx': 'xxx'}}
            registry.publish(resources.FLOATING_IP, events.BEFORE_DELETE,
                             self, payload=events.DBEventPayload(
                    context,
                    request_body=floatingip,
                    resource_id=floatingip['id']))
        except exceptions.CallbackFailure as e:
            # raise the underlying exception
            raise e.errors[0].error
        registry.notify(resources.FLOATING_IP, events.AFTER_DELETE,
                        self, **floatingip)
        self.l3_plugin._notify_floating_ip_change(context, floatingip)
        return {"message": "floating_ip deleted"}

    def _get_pf_fip_object(self, context, port_forwarding):
        floatingip_id = port_forwarding['floatingip_id']
        pf_obj = port_forwarding_obj.PortForwarding.get_object(
            context, id=port_forwarding["id"])
        if not pf_obj:
            raise pf_exc.PortForwardingNotFound(id=port_forwarding["id"])
        fip_obj = self.pf_plugin._get_fip_obj(context, floatingip_id)
        return pf_obj, fip_obj

    def port_forwarding_created(self, context, data):
        port_forwarding = data.get("port_forwarding")
        pf_obj, fip_obj = self._get_pf_fip_object(context, port_forwarding)
        router_id = self.pf_plugin._find_a_router_for_fip_port_forwarding(
            context, port_forwarding, fip_obj)
        self.push_api.push(context, [pf_obj], rpc_events.CREATED)
        # for metering
        if not cfg.CONF.is_sdn_arch and cfg.CONF.enable_meter_full_eip:
            registry.notify(port_forwarding_obj.PortForwarding.obj_name(),
                            events.AFTER_CREATE,
                            self, context=context,
                            project_id=fip_obj.project_id, pf_obj=pf_obj)
            LOG.debug("Notify create metering with "
                      "pf_obj=%s, project_id=%s",
                      pf_obj, fip_obj.project_id)

        self.l3_plugin.notify_router_updated(context, router_id)
        return {"message": "port_forwarding created"}

    def port_forwarding_updated(self, context, data):
        port_forwarding = data.get("port_forwarding")
        pf_obj, fip_obj = self._get_pf_fip_object(context, port_forwarding)
        self.push_api.push(context, [pf_obj], rpc_events.UPDATED)
        internal_ip_address = port_forwarding.get(
            'internal_ip_address')
        internal_port = port_forwarding.get('internal_port')
        protocol = port_forwarding.get('protocol')
        old_pf = data.get("old_port_forwarding")
        old_pf_obj = port_forwarding_obj.PortForwarding(context, **old_pf)
        # for metering
        if not cfg.CONF.is_sdn_arch and cfg.CONF.enable_meter_full_eip:
            registry.notify(port_forwarding_obj.PortForwarding.obj_name(),
                            events.AFTER_UPDATE,
                            self, context=context,
                            project_id=fip_obj.project_id,
                            pf_obj=old_pf_obj,
                            the_latest_ip_address=internal_ip_address,
                            the_larest_internal_port=internal_port,
                            the_latest_protocol=protocol)
            LOG.debug("Notify update metering with "
                      "new pf_obj=%s, project_id=%s",
                      pf_obj, fip_obj.project_id)
        return {"message": "port_forwarding updated"}

    def port_forwarding_deleted(self, context, data):
        # Please call this API first before deleting the object.
        port_forwarding = data.get("port_forwarding")
        pf_obj, fip_obj = self._get_pf_fip_object(context, port_forwarding)
        router_id = self.pf_plugin._find_a_router_for_fip_port_forwarding(
            context, port_forwarding, fip_obj)
        self.l3_plugin.notify_router_updated(context, router_id)
        self.push_api.push(context, [pf_obj], rpc_events.DELETED)
        # for metering
        if not cfg.CONF.is_sdn_arch and cfg.CONF.enable_meter_full_eip:
            registry.notify(port_forwarding_obj.PortForwarding.obj_name(),
                            events.AFTER_DELETE,
                            self, context=context,
                            project_id=fip_obj.project_id, pf_obj=pf_obj)
            LOG.debug("Notify delete metering with "
                      "pf_obj=%s, project_id=%s",
                      pf_obj, fip_obj.project_id)
        return {"message": "port_forwarding deleted"}

    def elastic_snat_created(self, context, data):
        elastic_snat = data.get("elastic_snat")
        esnat_obj = self.elastic_snat_plugin._get_elastic_snat(
            context, elastic_snat["id"])
        registry.publish(esnat_def.ELASTIC_SNAT, events.AFTER_CREATE, self,
                         payload=events.DBEventPayload(
                             context, states=(esnat_obj,),
                             resource_id=esnat_obj.id))
        self.elastic_snat_plugin.driver.create_elastic_snat(context, esnat_obj)
        return {"message": "elastic_snat created"}

    def elastic_snat_updated(self, context, data):
        elastic_snat = data.get("elastic_snat")
        esnat_obj = self.elastic_snat_plugin._get_elastic_snat(
            context, elastic_snat["id"])
        if ('subnets' in elastic_snat.keys()
                or 'internal_cidrs' in elastic_snat.keys()):
            registry.publish(esnat_def.ELASTIC_SNAT, events.AFTER_UPDATE, self,
                             payload=events.DBEventPayload(
                                 context, states=(esnat_obj,),
                                 resource_id=esnat_obj.id))
        self.elastic_snat_plugin.driver.update_elastic_snat(context, esnat_obj)
        return {"message": "elastic_snat updated"}

    def elastic_snat_deleted(self, context, data):
        # Please call this API first before deleting the object.
        elastic_snat = data.get("elastic_snat")
        esnat_obj = self.elastic_snat_plugin._get_elastic_snat(
            context, elastic_snat["id"])
        registry.publish(esnat_def.ELASTIC_SNAT, events.AFTER_DELETE, self,
                         payload=events.DBEventPayload(
                             context, states=(esnat_obj,),
                             resource_id=esnat_obj.id))
        self.elastic_snat_plugin.driver.delete_elastic_snat(context, esnat_obj)
        return {"message": "elastic_snat deleted"}

    def traffic_mirror_created(self, data):
        return {"message": "traffic_mirror created"}

    def traffic_mirror_updated(self, data):
        return {"message": "traffic_mirror updated"}

    def traffic_mirror_deleted(self, data):
        return {"message": "traffic_mirror deleted"}

    def security_group_created(self, data):
        return {"message": "security_group created"}

    def security_group_updated(self, data):
        return {"message": "security_group updated"}

    def security_group_deleted(self, data):
        return {"message": "security_group deleted"}

    def security_group_rule_created(self, data):
        return {"message": "security_group_rule created"}

    def security_group_rule_updated(self, data):
        return {"message": "security_group_rule updated"}

    def security_group_rule_deleted(self, data):
        return {"message": "security_group_rule deleted"}

    def default_handler(self, data):
        return {"error": "Unhandled event"}
